import sys
import os
import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision import transforms

# Add the DINOv3 repo path
REPO_DIR = "/storage/pmj/zhi/project/dinov3-main"
sys.path.append(REPO_DIR)

from dinov3.models.vision_transformer import DinoVisionTransformer
from dinov3.eval.dense.depth.models.dpt_head import DPTHead_decoder




def create_multiscale_decoder(embed_dim=1024, features=256):
    """Create DPTHead_decoder for multi-scale feature fusion."""
    decoder = DPTHead_decoder(
        in_channels=embed_dim,
        features=features,
        use_bn=False,
        out_channels=[256, 512, 1024, 1024],
        use_clstoken=True
    )
    return decoder


def extract_multiscale_features(backbone, decoder, x, layer_indices=[4, 11, 17, 23]):
    """Extract and fuse multi-scale features."""
    # Get intermediate layer features
    intermediate_features = backbone.get_intermediate_layers(
        x,
        n=layer_indices,
        reshape=False,
        return_class_token=True,
        return_extra_tokens=False,
        norm=True
    )
    
    # Calculate patch dimensions
    B, C, H, W = x.shape
    patch_h = H // backbone.patch_size
    patch_w = W // backbone.patch_size
    
    # Fuse features through decoder
    fused_features = decoder(intermediate_features, patch_h, patch_w)
    
    return fused_features


class Feature_extractor(nn.Module):
    """Encapsulates DINOv3 backbone + DPT multi-scale decoder into a single module.
    Forward returns a 4-level pyramid (1/4,1/8,1/16,1/32) given an input image tensor.
    """
    def __init__(self, checkpoint_path: str, layer_indices=None, features: int = 256, device: str | None = None):
        super().__init__()
        self.checkpoint_path = checkpoint_path
        self.layer_indices = layer_indices or [4, 11, 17, 23]
        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        if not os.path.exists(checkpoint_path):
            raise FileNotFoundError(f"Checkpoint not found: {checkpoint_path}")

        # ImageNet normalization for DINOv3
        self.normalize = transforms.Normalize(
            mean=(0.485, 0.456, 0.406),
            std=(0.229, 0.224, 0.225),
        )

        # Build backbone (inline) then load weights
        print(f"Building DINOv3 ViT-L/16 backbone and loading weights from: {checkpoint_path}")
        self.backbone = DinoVisionTransformer(
            img_size=224,
            patch_size=16,
            in_chans=3,
            pos_embed_rope_base=100,
            pos_embed_rope_normalize_coords="separate",
            pos_embed_rope_rescale_coords=2,
            pos_embed_rope_dtype="fp32",
            embed_dim=1024,
            depth=24,
            num_heads=16,
            ffn_ratio=4,
            qkv_bias=True,
            drop_path_rate=0.0,
            layerscale_init=1.0e-05,
            norm_layer="layernormbf16",
            ffn_layer="mlp",
            ffn_bias=True,
            proj_bias=True,
            n_storage_tokens=4,
            mask_k_bias=True,
            untie_global_and_local_cls_norm=False,
        )
        state_dict = torch.load(checkpoint_path, map_location="cpu")
        self.backbone.load_state_dict(state_dict, strict=True)
        print("✅ Backbone weights loaded.")
        # Decoder
        self.decoder = create_multiscale_decoder(embed_dim=self.backbone.embed_dim, features=features)
        
    def forward(self, x: torch.Tensor):
        """Original forward method for single image processing."""
        # Convert from [-1, 1] to [0, 1] range
        x = (x + 1.0) / 2.0
        # Apply ImageNet normalization
        x = self.normalize(x)

        intermediate = self.backbone.get_intermediate_layers(
            x,
            n=self.layer_indices,
            reshape=False,
            return_class_token=True,
            return_extra_tokens=False,
            norm=True,
        )
        B, C, H, W = x.shape
        patch_h = H // self.backbone.patch_size
        patch_w = W // self.backbone.patch_size
        pyramid = self.decoder(intermediate, patch_h, patch_w)
        return pyramid

    def infer_mono(self, image1: torch.Tensor, image2: torch.Tensor):
        """
        Replacement for selective_igev's infer_mono method.
        Processes left and right views together and returns separated features.

        Args:
            image1: Left view tensor, shape (B, C, H, W), range [-1, 1]
            image2: Right view tensor, shape (B, C, H, W), range [-1, 1]

        Returns:
            Tuple of (left_features, right_features) where each is a list of 4 feature maps
            at different scales [1/4, 1/8, 1/16, 1/32]
        """
        # Convert from [-1, 1] to [0, 1] range
        image1 = (image1 + 1.0) / 2.0
        image2 = (image2 + 1.0) / 2.0

        # Apply ImageNet normalization
        image1 = self.normalize(image1)
        image2 = self.normalize(image2)

        # Concatenate left and right views along batch dimension
        stacked = torch.cat([image1, image2], dim=0)

        # Extract intermediate features
        intermediate = self.backbone.get_intermediate_layers(
            stacked,
            n=self.layer_indices,
            reshape=False,
            return_class_token=True,
            return_extra_tokens=False,
            norm=True,
        )

        # Decode features
        f4x, f8x, f16x, f32x = self.decoder(intermediate, patch_h, patch_w)

        # Split features back into left and right views
        b = image1.shape[0]
        features_left_4x, features_right_4x = f4x[:b], f4x[b:]
        features_left_8x, features_right_8x = f8x[:b], f8x[b:]
        features_left_16x, features_right_16x = f16x[:b], f16x[b:]
        features_left_32x, features_right_32x = f32x[:b], f32x[b:]

        return ([features_left_4x, features_left_8x, features_left_16x, features_left_32x],
                [features_right_4x, features_right_8x, features_right_16x, features_right_32x])
    
    def extract(self, x: torch.Tensor):
        return self.forward(x)


def main():
    """Main function demonstrating multi-scale feature extraction."""
    # Configuration
    checkpoint_path = "checkpoint/dinov3_vitl16_pretrain_lvd1689m-8aa4cbdd.pth"
    layer_indices = [4, 11, 17, 23]  # ViT-L layers for multi-scale extraction
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    print(f"Using device: {device}")
    print("=" * 60)
    print("DINOv3 Multi-Scale Feature Extraction")
    print("=" * 60)
    
    # Build feature extractor module
    print("\n1. Building Feature_extractor module (loads backbone + decoder)...")
    extractor = Feature_extractor(checkpoint_path, layer_indices=layer_indices, features=256, device=device)
    
    # 2. Test with sample input
    print("\n2. Testing multi-scale feature extraction...")
    test_input = torch.randn(1, 3, 1024, 2048).to(device)
    
    with torch.no_grad():
        pyramid = extractor(test_input)
        print("   Fused multi-scale features (1/4,1/8,1/16,1/32):")
        for i, feat in enumerate(pyramid):
            scale_name = ["1/4","1/8","1/16","1/32"][i]
            print(f"   Level {i+1} ({scale_name}): {feat.shape}")
    
    print("\n✅ Multi-scale feature extraction completed successfully!")


if __name__ == "__main__":
    main()
