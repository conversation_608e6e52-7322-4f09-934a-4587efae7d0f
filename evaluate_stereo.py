from __future__ import print_function, division
import sys
sys.path.append('core')
import os
os.environ['CUDA_VISIBLE_DEVICES'] = '1'

import argparse
import time
import logging
import numpy as np
import torch
from tqdm import tqdm
from monster import Monster, autocast
import cv2

import stereo_datasets as datasets
from utils.utils import InputPadder
from PIL import Image
import torch.nn.functional as F

class NormalizeTensor(object):
    """Normalize a tensor by given mean and std."""
    
    def __init__(self, mean, std):
        self.mean = torch.tensor(mean)
        self.std = torch.tensor(std)
    
    def __call__(self, tensor):
        """
        Args:
            tensor (Tensor): Tensor image of size (C, H, W) to be normalized.
            
        Returns:
            Tensor: Normalized Tensor image.
        """
        # Ensure mean and std have the same number of channels as the input tensor
        Device = tensor.device
        self.mean = self.mean.to(Device)
        self.std = self.std.to(Device)

        # Normalize the tensor
        if self.mean.ndimension() == 1:
            self.mean = self.mean[:, None, None]
        if self.std.ndimension() == 1:
            self.std = self.std[:, None, None]

        return (tensor - self.mean) / self.std
    

def count_parameters(model):
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

@torch.no_grad()
def validate_eth3d(model, iters=32, mixed_prec=False):
    """ Peform validation using the ETH3D (train) split """
    model.eval()
    aug_params = {}
    val_dataset = datasets.ETH3D(aug_params)

    out_list, epe_list = [], []
    for val_id in range(len(val_dataset)):
        (imageL_file, imageR_file, GT_file), image1, image2, flow_gt, valid_gt = val_dataset[val_id]
        image1 = image1[None].cuda()
        image2 = image2[None].cuda()

        padder = InputPadder(image1.shape, divis_by=32)
        image1, image2 = padder.pad(image1, image2)
        with torch.no_grad():
            with autocast(enabled=mixed_prec):
                flow_pr = model(image1, image2, iters=iters, test_mode=True)

        flow_pr = padder.unpad(flow_pr.float()).cpu().squeeze(0)
        assert flow_pr.shape == flow_gt.shape, (flow_pr.shape, flow_gt.shape)
        epe = torch.sum((flow_pr - flow_gt)**2, dim=0).sqrt()

        epe_flattened = epe.flatten()

        occ_mask = Image.open(GT_file.replace('disp0GT.pfm', 'mask0nocc.png'))

        occ_mask = np.ascontiguousarray(occ_mask).flatten()

        val = (valid_gt.flatten() >= 0.5) & (occ_mask == 255)
        # val = (valid_gt.flatten() >= 0.5)
        out = (epe_flattened > 1.0)
        image_out = out[val].float().mean().item()
        image_epe = epe_flattened[val].mean().item()
        logging.info(f"ETH3D {val_id+1} out of {len(val_dataset)}. EPE {round(image_epe,4)} D1 {round(image_out,4)}")
        epe_list.append(image_epe)
        out_list.append(image_out)

    epe_list = np.array(epe_list)
    out_list = np.array(out_list)

    epe = np.mean(epe_list)
    d1 = 100 * np.mean(out_list)

    print("Validation ETH3D: EPE %f, D1 %f" % (epe, d1))
    return {'eth3d-epe': epe, 'eth3d-d1': d1}


@torch.no_grad()
def validate_kitti(model, iters=32, mixed_prec=False):
    """ Peform validation using the KITTI-2015 (train) split """
    model.eval()
    # aug_params = {'crop_size': list([540, 960])}
    aug_params = {}
    val_dataset = datasets.KITTI(aug_params, image_set='training')
    torch.backends.cudnn.benchmark = True

    out_list, epe_list, elapsed_list = [], [], []
    for val_id in range(len(val_dataset)):
        (imageL_file, _, _), image1, image2, flow_gt, valid_gt = val_dataset[val_id]
        image1 = image1[None].cuda()
        image2 = image2[None].cuda()
    
        padder = InputPadder(image1.shape, divis_by=32)
        image1, image2 = padder.pad(image1, image2)

        with torch.no_grad():
            with autocast(enabled=mixed_prec):
                start = time.time()
                flow_pr = model(image1, image2, iters=iters, test_mode=True)
                end = time.time()

        if val_id > 50:
            elapsed_list.append(end-start)
        flow_pr = padder.unpad(flow_pr).cpu().squeeze(0)

        assert flow_pr.shape == flow_gt.shape, (flow_pr.shape, flow_gt.shape)
        epe = torch.sum((flow_pr - flow_gt)**2, dim=0).sqrt()

        epe_flattened = epe.flatten()
        val = (valid_gt.flatten() >= 0.5) & (flow_gt.abs().flatten() < 192)
        # val = valid_gt.flatten() >= 0.5

        out = (epe_flattened > 3.0)
        image_out = out[val].float().mean().item()
        image_epe = epe_flattened[val].mean().item()
        if val_id < 9 or (val_id+1)%10 == 0:
            logging.info(f"KITTI Iter {val_id+1} out of {len(val_dataset)}. EPE {round(image_epe,4)} D1 {round(image_out,4)}. Runtime: {format(end-start, '.3f')}s ({format(1/(end-start), '.2f')}-FPS)")
        epe_list.append(epe_flattened[val].mean().item())
        out_list.append(out[val].cpu().numpy())

        # if val_id > 20:
        #     break

    epe_list = np.array(epe_list)
    out_list = np.concatenate(out_list)

    epe = np.mean(epe_list)
    d1 = 100 * np.mean(out_list)

    avg_runtime = np.mean(elapsed_list)

    print(f"Validation KITTI: EPE {epe}, D1 {d1}, {format(1/avg_runtime, '.2f')}-FPS ({format(avg_runtime, '.3f')}s)")
    return {'kitti-epe': epe, 'kitti-d1': d1}


@torch.no_grad()
def validate_vkitti(model, iters=32, mixed_prec=False):
    """ Peform validation using the vkitti (train) split """
    model.eval()
    aug_params = {}
    val_dataset = datasets.VKITTI2(aug_params)
    torch.backends.cudnn.benchmark = True

    out_list, epe_list, elapsed_list = [], [], []
    for val_id in range(len(val_dataset)):
        _, image1, image2, flow_gt, valid_gt = val_dataset[val_id]
        image1 = image1[None].cuda()
        image2 = image2[None].cuda()

        padder = InputPadder(image1.shape, divis_by=32)
        image1, image2 = padder.pad(image1, image2)

        with autocast(enabled=mixed_prec):
            start = time.time()
            flow_pr = model(image1, image2, iters=iters, test_mode=True)
            end = time.time()

        if val_id > 50:
            elapsed_list.append(end - start)
        flow_pr = padder.unpad(flow_pr).cpu().squeeze(0)

        assert flow_pr.shape == flow_gt.shape, (flow_pr.shape, flow_gt.shape)
        epe = torch.sum((flow_pr - flow_gt) ** 2, dim=0).sqrt()

        epe_flattened = epe.flatten()
        val = (valid_gt.flatten() >= 0.5) & (flow_gt.abs().flatten() < 192)
        # val = valid_gt.flatten() >= 0.5

        out = (epe_flattened > 3.0)
        image_out = out[val].float().mean().item()
        image_epe = epe_flattened[val].mean().item()
        if val_id < 9 or (val_id + 1) % 10 == 0:
            logging.info(
                f"VKITTI Iter {val_id + 1} out of {len(val_dataset)}. EPE {round(image_epe, 4)} D1 {round(image_out, 4)}. Runtime: {format(end - start, '.3f')}s ({format(1 / (end - start), '.2f')}-FPS)")
        epe_list.append(epe_flattened[val].mean().item())
        out_list.append(out[val].cpu().numpy())

        # if val_id > 20:
        #     break

    epe_list = np.array(epe_list)
    out_list = np.concatenate(out_list)

    epe = np.mean(epe_list)
    d1 = 100 * np.mean(out_list)

    avg_runtime = np.mean(elapsed_list)

    print(f"Validation VKITTI: EPE {epe}, D1 {d1}, {format(1 / avg_runtime, '.2f')}-FPS ({format(avg_runtime, '.3f')}s)")
    return {'vkitti-epe': epe, 'vkitti-d1': d1}



# @torch.no_grad()
# def validate_sceneflow(model, iters=32, mixed_prec=False):
#     """ Peform validation using the Scene Flow (TEST) split """
#     model.eval()
#     val_dataset = datasets.SceneFlowDatasets(dstype='frames_finalpass', things_test=True)
#     torch.backends.cudnn.benchmark = True

#     out_list, epe_list, elapsed_list = [], [], []
#     for val_id in tqdm(range(len(val_dataset))):
#         _, image1, image2, flow_gt, valid_gt = val_dataset[val_id]

#         image1 = image1[None].cuda()
#         image2 = image2[None].cuda()

#         padder = InputPadder(image1.shape, divis_by=32)
#         image1, image2 = padder.pad(image1, image2)

#         with autocast(enabled=mixed_prec):
#             start = time.time()
#             flow_pr = model(image1, image2, iters=iters, test_mode=True)
#             end = time.time()
#         # print(torch.cuda.memory_summary(device=None, abbreviated=False))
#         if val_id > 50:
#             elapsed_list.append(end-start)

#         flow_pr = padder.unpad(flow_pr).cpu().squeeze(0)
#         assert flow_pr.shape == flow_gt.shape, (flow_pr.shape, flow_gt.shape)

#         # epe = torch.sum((flow_pr - flow_gt)**2, dim=0).sqrt()
#         epe = torch.abs(flow_pr - flow_gt)

#         epe = epe.flatten()
#         val = (valid_gt.flatten() >= 0.5) & (flow_gt.abs().flatten() < 192)

#         if(np.isnan(epe[val].mean().item())):
#             continue

#         out = (epe > 3.0)
#         image_out = out[val].float().mean().item()
#         image_epe = epe[val].mean().item()
#         if val_id < 9 or (val_id + 1) % 10 == 0:
#             logging.info(
#                 f"Scene Flow Iter {val_id + 1} out of {len(val_dataset)}. EPE {round(image_epe, 4)} D1 {round(image_out, 4)}. Runtime: {format(end - start, '.3f')}s ({format(1 / (end - start), '.2f')}-FPS)")

#         print('epe', epe[val].mean().item())
#         epe_list.append(epe[val].mean().item())
#         out_list.append(out[val].cpu().numpy())

#     epe_list = np.array(epe_list)
#     out_list = np.concatenate(out_list)

#     epe = np.mean(epe_list)
#     d1 = 100 * np.mean(out_list)

#     avg_runtime = np.mean(elapsed_list)
#     # f = open('test.txt', 'a')
#     # f.write("Validation Scene Flow: %f, %f\n" % (epe, d1))

#     print(f"Validation Scene Flow: EPE {epe}, D1 {d1}, {format(1/avg_runtime, '.2f')}-FPS ({format(avg_runtime, '.3f')}s)" )
#     return {'scene-disp-epe': epe, 'scene-disp-d1': d1}



@torch.no_grad()
def validate_sceneflow(model, iters=32, mixed_prec=False):
    """ Peform validation using the Scene Flow (TEST) split """
    model.eval()
    val_dataset = datasets.SceneFlowDatasets(dstype='frames_finalpass', things_test=True)
    val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=8, pin_memory=True, shuffle=False, num_workers=4, drop_last=False)

    # 三个区域的指标列表：Overall, Edge, Non-Edge
    out_list_overall, epe_list_overall = [], []
    out_list_edge, epe_list_edge = [], []
    out_list_nonedge, epe_list_nonedge = [], []
    
    for i_batch, (_, *data_blob) in enumerate(tqdm(val_loader)):
        image1, image2, flow_gt, valid_gt = [x.cuda() for x in data_blob]

        padder = InputPadder(image1.shape, divis_by=32)
        image1, image2 = padder.pad(image1, image2)

        with autocast(enabled=mixed_prec):
            flow_pr = model(image1, image2, iters=iters, test_mode=True)
        flow_pr = padder.unpad(flow_pr)
        assert flow_pr.shape == flow_gt.shape, (flow_pr.shape, flow_gt.shape)

        epe = torch.abs(flow_pr - flow_gt)
        
        # 对batch中的每个样本进行处理
        batch_size = flow_gt.shape[0]
        
        for b in range(batch_size):
            # 获取单个样本的数据
            epe_single = epe[b].flatten()
            flow_gt_single = flow_gt[b]
            valid_gt_single = valid_gt[b]
            
            # 基本的有效性mask
            val_basic = (valid_gt_single.flatten() >= 0.5) & (flow_gt_single.abs().flatten() < 192)
            
            # 使用Canny边缘检测在Ground Truth视差图上检测边缘
            # 将视差图转换为numpy数组并归一化到0-255范围用于Canny检测
            flow_gt_np = flow_gt_single.cpu().numpy().squeeze()
            if len(flow_gt_np.shape) > 2:
                flow_gt_np = flow_gt_np[0]  # 取第一个通道
            
            # 归一化到0-255范围
            flow_gt_normalized = cv2.normalize(flow_gt_np, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
            
            # 应用Canny边缘检测
            edges = cv2.Canny(flow_gt_normalized, threshold1=50, threshold2=150)
            
            # 将边缘图变大一些，使用膨胀操作
            kernel = np.ones((3, 3), np.uint8)
            edges = cv2.dilate(edges, kernel, iterations=1)

            # 保存边缘图的
            # cv2.imwrite(f'edge_{i_batch}.png', edges)

            edges_tensor = torch.from_numpy(edges).flatten().bool().cuda()
            
            # 创建边缘和非边缘区域的mask
            edge_mask = val_basic & edges_tensor
            nonedge_mask = val_basic & (~edges_tensor)
            
            # 计算各区域的指标
            out_basic = (epe_single > 1.0).float()
            
            # Overall区域（原有逻辑）
            if val_basic.sum() > 0:
                epe_list_overall.append(epe_single[val_basic].mean().item())
                out_list_overall.append(out_basic[val_basic].mean().item())
            
            # Edge区域
            if edge_mask.sum() > 0:
                epe_list_edge.append(epe_single[edge_mask].mean().item())
                out_list_edge.append(out_basic[edge_mask].mean().item())
            
            # Non-Edge区域
            if nonedge_mask.sum() > 0:
                epe_list_nonedge.append(epe_single[nonedge_mask].mean().item())
                out_list_nonedge.append(out_basic[nonedge_mask].mean().item())

        if i_batch < 9 or (i_batch + 1) % 10 == 0:
            # 计算当前的平均值
            current_epe_overall = np.mean(epe_list_overall) if len(epe_list_overall) > 0 else 0.0
            current_d1_overall = 100 * np.mean(out_list_overall) if len(out_list_overall) > 0 else 0.0
            
            current_epe_edge = np.mean(epe_list_edge) if len(epe_list_edge) > 0 else 0.0
            current_d1_edge = 100 * np.mean(out_list_edge) if len(out_list_edge) > 0 else 0.0
            
            current_epe_nonedge = np.mean(epe_list_nonedge) if len(epe_list_nonedge) > 0 else 0.0
            current_d1_nonedge = 100 * np.mean(out_list_nonedge) if len(out_list_nonedge) > 0 else 0.0
            
            print(f"Scene Flow Iter {i_batch + 1}/{len(val_loader)} - "
                  f"Overall: EPE {round(current_epe_overall, 4)} D1 {round(current_d1_overall, 4)} | "
                  f"Edge: EPE {round(current_epe_edge, 4)} D1 {round(current_d1_edge, 4)} | "
                  f"Non-Edge: EPE {round(current_epe_nonedge, 4)} D1 {round(current_d1_nonedge, 4)}")

    # 计算最终结果
    epe_overall = np.mean(epe_list_overall) if len(epe_list_overall) > 0 else 0.0
    d1_overall = 100 * np.mean(out_list_overall) if len(out_list_overall) > 0 else 0.0
    
    epe_edge = np.mean(epe_list_edge) if len(epe_list_edge) > 0 else 0.0
    d1_edge = 100 * np.mean(out_list_edge) if len(out_list_edge) > 0 else 0.0
    
    epe_nonedge = np.mean(epe_list_nonedge) if len(epe_list_nonedge) > 0 else 0.0
    d1_nonedge = 100 * np.mean(out_list_nonedge) if len(out_list_nonedge) > 0 else 0.0

    # 输出结果
    print("\n" + "="*80)
    print("SCENE FLOW VALIDATION RESULTS:")
    print("="*80)
    print(f"Overall Region:   EPE {epe_overall:.4f}, D1 {d1_overall:.4f}")
    print(f"Edge Region:      EPE {epe_edge:.4f}, D1 {d1_edge:.4f}")
    print(f"Non-Edge Region:  EPE {epe_nonedge:.4f}, D1 {d1_nonedge:.4f}")
    print("="*80)
    
    # 写入到text.txt文件
    with open('text.txt', 'a') as f:
        f.write(f"Validation Scene Flow Results:\n")
        f.write(f"Overall Region:   EPE {epe_overall:.4f}, D1 {d1_overall:.4f}\n")
        f.write(f"Edge Region:      EPE {epe_edge:.4f}, D1 {d1_edge:.4f}\n")
        f.write(f"Non-Edge Region:  EPE {epe_nonedge:.4f}, D1 {d1_nonedge:.4f}\n")
        f.write("-" * 50 + "\n")
    
    return {
        'scene-flow-epe': epe_overall, 
        'scene-flow-d1': d1_overall,
        'scene-flow-edge-epe': epe_edge,
        'scene-flow-edge-d1': d1_edge,
        'scene-flow-nonedge-epe': epe_nonedge,
        'scene-flow-nonedge-d1': d1_nonedge
    }




@torch.no_grad()
def validate_driving(model, iters=32, mixed_prec=False):
    """ Peform validation using the DrivingStereo (test) split """
    model.eval()
    aug_params = {}
    # val_dataset = datasets.DrivingStereo(aug_params, image_set='test')
    val_dataset = datasets.DrivingStereo(aug_params, image_set='cloudy')
    print(len(val_dataset))
    torch.backends.cudnn.benchmark = True

    out_list, epe_list, elapsed_list = [], [], []
    out1_list, out2_list = [], []
    for val_id in range(len(val_dataset)):
        _, image1, image2, flow_gt, valid_gt = val_dataset[val_id]
        image1 = image1[None].cuda()
        image2 = image2[None].cuda()

        padder = InputPadder(image1.shape, divis_by=32)
        image1, image2 = padder.pad(image1, image2)

        with torch.autocast(device_type='cuda', enabled=mixed_prec):
            start = time.time()
            flow_pr = model(image1, image2, iters=iters, test_mode=True)
            end = time.time()

        if val_id > 50:
            elapsed_list.append(end-start)
        flow_pr = padder.unpad(flow_pr).cpu().squeeze(0)

        assert flow_pr.shape == flow_gt.shape, (flow_pr.shape, flow_gt.shape)
        epe = torch.sum((flow_pr - flow_gt)**2, dim=0).sqrt()

        epe_flattened = epe.flatten()
        val = (valid_gt.flatten() >= 0.5) & (flow_gt.abs().flatten() < 192)
        # val = valid_gt.flatten() >= 0.5

        out = (epe_flattened > 3.0)
        out1 = (epe_flattened > 1.0)
        out2 = (epe_flattened > 2.0)
        image_out = out[val].float().mean().item()
        image_epe = epe_flattened[val].mean().item()
        if val_id < 9 or (val_id+1)%10 == 0:
            logging.info(f"Driving Iter {val_id+1} out of {len(val_dataset)}. EPE {round(image_epe,4)} D1 {round(image_out,4)}. Runtime: {format(end-start, '.3f')}s ({format(1/(end-start), '.2f')}-FPS)")
        epe_list.append(epe_flattened[val].mean().item())
        out_list.append(out[val].cpu().numpy())
        out1_list.append(out1[val].cpu().numpy())
        out2_list.append(out2[val].cpu().numpy())

    epe_list = np.array(epe_list)
    out_list = np.concatenate(out_list)
    out1_list = np.concatenate(out1_list)
    out2_list = np.concatenate(out2_list)

    epe = np.mean(epe_list)
    d1 = 100 * np.mean(out_list)
    bad_2 = 100 * np.mean(out2_list)
    bad_1 = 100 * np.mean(out1_list)
    avg_runtime = np.mean(elapsed_list)

    print(f"Validation DrivingStereo: EPE {epe}, bad1 {bad_1}, bad2 {bad_2}, bad3 {d1}, {format(1/avg_runtime, '.2f')}-FPS ({format(avg_runtime, '.3f')}s)")
    return {'driving-epe': epe, 'driving-d1': d1}


@torch.no_grad()
def validate_middlebury(model, iters=32, split='F', mixed_prec=False):
    """ Peform validation using the Middlebury-V3 dataset """
    model.eval()
    aug_params = {}
    val_dataset = datasets.Middlebury(aug_params, split=split)

    out_list, epe_list = [], []
    for val_id in range(len(val_dataset)):
        (imageL_file, _, _), image1, image2, flow_gt, valid_gt = val_dataset[val_id]
        image1 = image1[None].cuda()
        image2 = image2[None].cuda()

        padder = InputPadder(image1.shape, divis_by=32)
        image1, image2 = padder.pad(image1, image2)

        with autocast(enabled=mixed_prec):
            flow_pr = model(image1, image2, iters=iters, test_mode=True)
        flow_pr = padder.unpad(flow_pr).cpu().squeeze(0)
        a = input('input something')
        print(a)

        assert flow_pr.shape == flow_gt.shape, (flow_pr.shape, flow_gt.shape)
        epe = torch.sum((flow_pr - flow_gt)**2, dim=0).sqrt()

        epe_flattened = epe.flatten()

        occ_mask = Image.open(imageL_file.replace('im0.png', 'mask0nocc.png')).convert('L')
        occ_mask = np.ascontiguousarray(occ_mask, dtype=np.float32).flatten()

        val = (valid_gt.reshape(-1) >= 0.5) & (flow_gt[0].reshape(-1) < 192) & (occ_mask==255)
        out = (epe_flattened > 2.0)
        image_out = out[val].float().mean().item()
        image_epe = epe_flattened[val].mean().item()
        logging.info(f"Middlebury Iter {val_id+1} out of {len(val_dataset)}. EPE {round(image_epe,4)} D1 {round(image_out,4)}")
        epe_list.append(image_epe)
        out_list.append(image_out)

    epe_list = np.array(epe_list)
    out_list = np.array(out_list)

    epe = np.mean(epe_list)
    d1 = 100 * np.mean(out_list)

    print(f"Validation Middlebury{split}: EPE {epe}, D1 {d1}")
    return {f'middlebury{split}-epe': epe, f'middlebury{split}-d1': d1}


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--restore_ckpt', help="restore checkpoint", default="checkpoint/sceneflow.pth")

    parser.add_argument('--dataset', help="dataset for evaluation", default='sceneflow', choices=["eth3d", "kitti", "sceneflow", "vkitti", "driving"] + [f"middlebury_{s}" for s in 'FHQ'])
    parser.add_argument('--mixed_precision', default=False, action='store_true', help='use mixed precision')
    parser.add_argument('--valid_iters', type=int, default=32, help='number of flow-field updates during forward pass')

    # Architecure choices
    parser.add_argument('--encoder', type=str, default='vitl', choices=['vits', 'vitb', 'vitl', 'vitg'])
    parser.add_argument('--hidden_dims', nargs='+', type=int, default=[128]*3, help="hidden state and context dimensions")
    parser.add_argument('--corr_implementation', choices=["reg", "alt", "reg_cuda", "alt_cuda"], default="reg", help="correlation volume implementation")
    parser.add_argument('--shared_backbone', action='store_true', help="use a single backbone for the context and feature encoders")
    parser.add_argument('--corr_levels', type=int, default=2, help="number of levels in the correlation pyramid")
    parser.add_argument('--corr_radius', type=int, default=4, help="width of the correlation pyramid")
    parser.add_argument('--n_downsample', type=int, default=2, help="resolution of the disparity field (1/2^K)")
    parser.add_argument('--slow_fast_gru', action='store_true', help="iterate the low-res GRUs more frequently")
    parser.add_argument('--n_gru_layers', type=int, default=3, help="number of hidden GRU levels")
    parser.add_argument('--max_disp', type=int, default=192, help="max disp of geometry encoding volume")
    args = parser.parse_args()

    model = torch.nn.DataParallel(Monster(args), device_ids=[0])

    total_params = sum(p.numel() for p in model.parameters()) / 1e6
    print(f"Total number of parameters: {total_params:.2f}M")

    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad) / 1e6
    print(f"Total number of trainable parameters: {trainable_params:.2f}M")

    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s %(levelname)-8s [%(filename)s:%(lineno)d] %(message)s')

    if args.restore_ckpt is not None:
        assert args.restore_ckpt.endswith(".pth")
        logging.info("Loading checkpoint...")
        logging.info(args.restore_ckpt)
        assert os.path.exists(args.restore_ckpt)
        checkpoint = torch.load(args.restore_ckpt)
        ckpt = dict()
        if 'state_dict' in checkpoint.keys():
            checkpoint = checkpoint['state_dict']
        for key in checkpoint:
            # ckpt['module.' + key] = checkpoint[key]
            if key.startswith("module."):
                ckpt[key] = checkpoint[key]  # 保持原样
            else:
                ckpt["module." + key] = checkpoint[key]  # 添加 "module."

        model.load_state_dict(ckpt, strict=True)

        logging.info(f"Done loading checkpoint")

    model.cuda()
    model.eval()

    print(f"The model has {format(count_parameters(model)/1e6, '.2f')}M learnable parameters.")
    use_mixed_precision = args.corr_implementation.endswith("_cuda")

    if args.dataset == 'eth3d':
        validate_eth3d(model, iters=args.valid_iters, mixed_prec=use_mixed_precision)

    elif args.dataset == 'kitti':
        validate_kitti(model, iters=args.valid_iters, mixed_prec=use_mixed_precision)

    elif args.dataset in [f"middlebury_{s}" for s in 'FHQ']:
        validate_middlebury(model, iters=args.valid_iters, split=args.dataset[-1], mixed_prec=use_mixed_precision)

    elif args.dataset == 'sceneflow':
        validate_sceneflow(model, iters=args.valid_iters, mixed_prec=use_mixed_precision)

    elif args.dataset == 'vkitti':
        validate_vkitti(model, iters=args.valid_iters, mixed_prec=use_mixed_precision)

    elif args.dataset == 'driving':
        validate_driving(model, iters=args.valid_iters, mixed_prec=use_mixed_precision)
