# DINOv3 Feature Extractor Integration Summary

## 概述

本次修改将 `dinov3-main/feature_extractor.py` 中的 `forward` 方法进行了改造，用来替换 `core/selective_igev.py` 中的 `infer_mono` 方法。主要目标是使用 DINOv3 特征提取器来处理立体视觉的左右视图。

## 主要修改

### 1. `dinov3-main/feature_extractor.py` 的修改

#### 新增导入
```python
import torch.nn.functional as F
from torchvision import transforms
```

#### 新增归一化处理
在 `Feature_extractor` 类的 `__init__` 方法中添加了 ImageNet 标准化：
```python
self.normalize = transforms.Normalize(
    mean=(0.485, 0.456, 0.406),
    std=(0.229, 0.224, 0.225),
)
```

#### 修改原有 `forward` 方法
- 添加了从 `[-1, 1]` 到 `[0, 1]` 的范围转换
- 应用 ImageNet 归一化
- 保持原有的单图像处理逻辑

#### 新增 `infer_mono` 方法
这是核心的新增方法，用于替换 `selective_igev` 的 `infer_mono`：

**方法签名：**
```python
def infer_mono(self, image1: torch.Tensor, image2: torch.Tensor, use_ckpt: bool = False)
```

**功能特点：**
1. **输入处理**：接受两个图像张量（左视图和右视图），范围为 `[-1, 1]`
2. **归一化**：
   - 将输入从 `[-1, 1]` 转换到 `[0, 1]`
   - 应用 ImageNet 标准化 `(mean=(0.485, 0.456, 0.406), std=(0.229, 0.224, 0.225))`
3. **批次拼接**：将左右视图沿批次维度拼接
4. **特征提取**：使用 DINOv3 backbone 提取中间层特征
5. **特征解码**：通过 DPT decoder 生成多尺度特征
6. **结果分离**：将拼接的特征分离回左右视图
7. **内存优化**：支持 `use_ckpt` 参数进行梯度检查点以节省显存

**内部实现：**
- `_infer_mono_plain()`: 标准推理，不使用梯度检查点
- `_infer_mono_ckpt()`: 使用梯度检查点的推理，节省显存但增加计算时间

**输出格式：**
```python
return (left_features, right_features)
```
其中每个 `features` 是包含 4 个特征图的列表，对应尺度 `[1/4, 1/8, 1/16, 1/32]`

### 2. `core/selective_igev.py` 的修改

#### 修复导入错误
```python
# 修改前
from dinove-main.feature_extractor import Feature_extractor

# 修改后  
from dinov3-main.feature_extractor import Feature_extractor
```

## 使用方法

### 在 selective_igev 中集成

```python
class SelectiveIGEV(nn.Module):
    def __init__(self, args):
        super().__init__()
        # 初始化 DINOv3 特征提取器
        self.feature_extractor = Feature_extractor(
            checkpoint_path="path/to/dinov3_checkpoint.pth",
            layer_indices=[4, 11, 17, 23],
            features=256
        )

    def forward(self, image1, image2, test_mode=False, ...):
        # 使用新的特征提取方法，支持梯度检查点
        use_ckpt = not test_mode  # 训练时使用检查点，测试时不使用
        left_features, right_features = self.feature_extractor.infer_mono(
            image1, image2, use_ckpt=use_ckpt
        )

        # 继续后续处理...
        return result
```

### 直接使用

```python
# 初始化特征提取器
extractor = Feature_extractor(checkpoint_path, layer_indices=[4, 11, 17, 23])

# 处理立体图像对
# 标准模式（不使用梯度检查点）
left_features, right_features = extractor.infer_mono(left_image, right_image, use_ckpt=False)

# 内存优化模式（使用梯度检查点）
left_features, right_features = extractor.infer_mono(left_image, right_image, use_ckpt=True)
```

## 技术细节

### 输入要求
- **图像格式**：`torch.Tensor`，形状为 `(B, C, H, W)`
- **数值范围**：`[-1, 1]`（已归一化）
- **通道顺序**：RGB

### 输出格式
- **左右特征**：每个都是包含 4 个张量的列表
- **尺度对应**：`[1/4, 1/8, 1/16, 1/32]` 相对于输入分辨率
- **特征维度**：由 `features` 参数控制（默认 256）

### 关键处理步骤
1. **范围转换**：`[-1, 1] → [0, 1]`
2. **ImageNet 归一化**：应用标准的 mean/std
3. **尺寸调整**：`scale_factor = 14/16 = 0.875`
4. **批次处理**：左右视图合并处理，提高效率
5. **特征分离**：确保输出格式与原 `infer_mono` 兼容

## 兼容性

- ✅ 与原 `selective_igev.infer_mono` 输出格式完全兼容
- ✅ 支持批次处理
- ✅ 保持原有的多尺度特征结构
- ✅ GPU/CPU 自适应

## 测试

提供了测试脚本：
- `test_feature_extractor_integration.py`：验证集成功能
- `usage_example.py`：使用示例

## use_ckpt 参数说明

### 什么是梯度检查点（Gradient Checkpointing）？
梯度检查点是一种内存优化技术，通过在反向传播时重新计算中间激活值来节省显存。

### 使用建议：
- **训练时**：`use_ckpt=True` - 节省显存，允许更大的批次大小
- **推理时**：`use_ckpt=False` - 更快的推理速度
- **显存不足时**：`use_ckpt=True` - 减少显存占用

### 性能对比：
| 模式 | 显存占用 | 推理速度 | 适用场景 |
|------|----------|----------|----------|
| `use_ckpt=False` | 高 | 快 | 推理、显存充足时的训练 |
| `use_ckpt=True` | 低 | 慢 | 显存不足时的训练 |

## 注意事项

1. **检查点路径**：确保 DINOv3 检查点文件存在
2. **内存使用**：DINOv3 模型较大，注意 GPU 内存
3. **依赖项**：确保安装了 `torchvision` 和相关依赖
4. **设备兼容**：模型会自动适配可用的 GPU/CPU
5. **use_ckpt 选择**：根据显存情况和使用场景选择合适的模式

## 性能优化建议

1. 使用 `torch.no_grad()` 进行推理
2. 考虑使用 `torch.jit.script` 进行模型优化
3. 批次大小根据 GPU 内存调整
4. 可以考虑使用 checkpoint 技术节省内存（参考原 `infer_mono_ckpt`）
