#!/usr/bin/env python3
"""
Debug script to compare feature shapes between original infer_mono and DINOv3 feature extractor.
"""

import torch
import sys
import os

# Add paths
sys.path.append('/storage/pmj/zhi/project/dssm')
sys.path.append('/storage/pmj/zhi/project/dssm/core')

def debug_feature_shapes():
    """Debug the feature shapes from both methods."""
    
    print("🔍 Debugging feature shapes...")
    
    try:
        from selective_igev import IGEVStereo
        
        # Create args
        class Args:
            def __init__(self):
                self.encoder = 'vitl'
                self.max_disp = 768
                self.mixed_precision = False
                self.n_gru_layers = 3
                self.hidden_dims = [128, 128, 128]
                self.corr_levels = 2
                self.corr_radius = 4
                self.n_downsample = 2
                self.level_switch_iter = 8
                self.enable_level_optimization = True
                self.neighborhood_refine_iters = 4
                self.neighborhood_size = 7
                self.enable_cross_attention = True
                self.enable_self_attention = True
                self.confidence_threshold = 0.5
                self.detail_enhancement = True
        
        args = Args()
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Create test images
        image1 = torch.randn(1, 3, 512, 1024).to(device) * 255  # Range [0, 255]
        image2 = torch.randn(1, 3, 512, 1024).to(device) * 255
        
        print(f"Input shapes: image1={image1.shape}, image2={image2.shape}")
        print(f"Input range: [{image1.min():.1f}, {image1.max():.1f}]")
        
        # Initialize model
        print("\n📦 Loading model...")
        model = IGEVStereo(args).to(device)
        model.eval()
        
        with torch.no_grad():
            # Normalize images like in the forward method
            image1_norm = (2 * (image1 / 255.0) - 1.0).contiguous()
            image2_norm = (2 * (image2 / 255.0) - 1.0).contiguous()
            
            print(f"Normalized range: [{image1_norm.min():.3f}, {image1_norm.max():.3f}]")
            
            # Test original infer_mono
            print("\n🔍 Testing original infer_mono...")
            try:
                orig_left, orig_right = model.infer_mono(image1_norm, image2_norm, use_ckpt=False)
                print("✅ Original infer_mono succeeded")
                print("Original left features shapes:")
                for i, feat in enumerate(orig_left):
                    print(f"  Level {i}: {feat.shape}")
                print("Original right features shapes:")
                for i, feat in enumerate(orig_right):
                    print(f"  Level {i}: {feat.shape}")
            except Exception as e:
                print(f"❌ Original infer_mono failed: {e}")
                orig_left, orig_right = None, None
            
            # Test DINOv3 feature extractor
            print("\n🔍 Testing DINOv3 feature extractor...")
            try:
                dinov3_left, dinov3_right = model.feature_extractor.infer_mono(image1_norm, image2_norm, use_ckpt=False)
                print("✅ DINOv3 feature extractor succeeded")
                print("DINOv3 left features shapes:")
                for i, feat in enumerate(dinov3_left):
                    print(f"  Level {i}: {feat.shape}")
                print("DINOv3 right features shapes:")
                for i, feat in enumerate(dinov3_right):
                    print(f"  Level {i}: {feat.shape}")
            except Exception as e:
                print(f"❌ DINOv3 feature extractor failed: {e}")
                import traceback
                traceback.print_exc()
                dinov3_left, dinov3_right = None, None
            
            # Compare shapes if both succeeded
            if orig_left is not None and dinov3_left is not None:
                print("\n📊 Comparing feature shapes:")
                for i in range(len(orig_left)):
                    orig_shape = orig_left[i].shape
                    dinov3_shape = dinov3_left[i].shape
                    match = "✅" if orig_shape == dinov3_shape else "❌"
                    print(f"  Level {i}: Original={orig_shape}, DINOv3={dinov3_shape} {match}")
            
            # Test feat_transfer with both inputs
            if orig_left is not None:
                print("\n🔍 Testing feat_transfer with original features...")
                try:
                    orig_transferred = model.feat_transfer(orig_left)
                    print("✅ feat_transfer with original features succeeded")
                    print("Transferred shapes:")
                    for i, feat in enumerate(orig_transferred):
                        print(f"  Level {i}: {feat.shape}")
                except Exception as e:
                    print(f"❌ feat_transfer with original features failed: {e}")
            
            if dinov3_left is not None:
                print("\n🔍 Testing feat_transfer with DINOv3 features...")
                try:
                    dinov3_transferred = model.feat_transfer(dinov3_left)
                    print("✅ feat_transfer with DINOv3 features succeeded")
                    print("Transferred shapes:")
                    for i, feat in enumerate(dinov3_transferred):
                        print(f"  Level {i}: {feat.shape}")
                except Exception as e:
                    print(f"❌ feat_transfer with DINOv3 features failed: {e}")
                    import traceback
                    traceback.print_exc()
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=" * 60)
    print("Feature Shapes Debug")
    print("=" * 60)
    
    debug_feature_shapes()
    
    print("\n✅ Debug completed!")
