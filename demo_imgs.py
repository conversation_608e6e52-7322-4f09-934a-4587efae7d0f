import sys
sys.path.append("core")

import argparse
import glob
import numpy as np
import torch
from tqdm import tqdm
from pathlib import Path
# from core.monster import Monster
from core.selective_igev import IGEVStereo as Monster
from core.utils.utils import InputPadder
from PIL import Image
from matplotlib import pyplot as plt
import os
import torch.nn.functional as F


DEVICE = "cuda"
import os
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

def load_image(imfile):
    img = np.array(Image.open(imfile)).astype(np.uint8)[..., :3]
    img = torch.from_numpy(img).permute(2, 0, 1).float()
    return img[None].to(DEVICE)

def get_file_stem(imfile, dataset):
    """根据数据集类型获取文件名"""
    if dataset in ("md", "md_F", "md_H"):
        return imfile.split("/")[-2]
    elif dataset == "booster":
        return imfile.split("/")[-3] + "_" + imfile.split("/")[-1].split('.')[0]
    elif dataset == "kitti":
        return imfile.split('/')[-1].split('.')[0]  # 去掉扩展名
    elif dataset == "sceneflow":
        return imfile.split("/")[-3] + "_" + imfile.split("/")[-1].split('.')[0]
    elif dataset == "drivingstereo":
        return imfile.split("/")[-3] + "_" + imfile.split("/")[-1].split('.')[0]
    else:
        return imfile.split("/")[-1].split('.')[0]  # 默认使用文件名

def get_dataset_config(dataset):
    """根据数据集类型返回配置信息"""
    configs = {
        "sceneflow": {   
            "left_imgs": "/storage/pmj/zhi/dataset/sceneFlow/frames_finalpass/TRAIN/B/0000/left/000*.png",
            "right_imgs": "/storage/pmj/zhi/dataset/sceneFlow/frames_finalpass/TRAIN/B/0000/right/000*.png",
            "output_dir": "output/sceneflow",
            "checkpoint": "checkpoints/pretrain/step_25000_igev-stereo-ddp.pth"
        },
        "md": {
            "left_imgs": "/storage/pmj/zhi/dataset/md/MiddEval3/*H/*/im0.png",
            "right_imgs": "/storage/pmj/zhi/dataset/md/MiddEval3/*H/*/im1.png",
            "output_dir": "output/md",
            "checkpoint": "checkpoints/pretrain/step_25000_igev-stereo-ddp.pth"
        },
        "md_H": {
            "left_imgs": "/storage/pmj/zhi/dataset/md/MiddEval3/*H/*/im0.png",
            "right_imgs": "/storage/pmj/zhi/dataset/md/MiddEval3/*H/*/im1.png",
            "output_dir": "output/md_H",
            "checkpoint": "checkpoints/pretrain/step_25000_igev-stereo-ddp.pth"
        },
        "md_F": {
            "left_imgs": "/storage/pmj/zhi/dataset/md/MiddEval3/*F/*/im0.png",
            "right_imgs": "/storage/pmj/zhi/dataset/md/MiddEval3/*F/*/im1.png",
            "output_dir": "output/md_F",
            "checkpoint": "checkpoints/pretrain/step_25000_igev-stereo-ddp.pth"
        },
        "kitti": {
            "left_imgs": "/storage/pmj/zhi/dataset/KITTI/KITTI 2015/testing/image_2/*_10.png",
            "right_imgs": "/storage/pmj/zhi/dataset/KITTI/KITTI 2015/testing/image_3/*_10.png",
            "output_dir": "output/kitti",
            "checkpoint": "checkpoints/kitti/step_9000_igev-stereo-kt-ddp.pth"
        },
        "drivingstereo": {
            "left_imgs": "/storage/pmj/zhi/dataset/drivingStereo/s*/left-image-full-size/*.png",
            "right_imgs": "/storage/pmj/zhi/dataset/drivingStereo/s*/right-image-full-size/*.png",
            "output_dir": "output/drivingstereo",
            "checkpoint": "checkpoints/pretrain/step_25000_igev-stereo-ddp.pth"
        },
        "booster": {
            "left_imgs": "/storage/pmj/zhi/dataset/Booster/test/balanced/*/camera_00/im0.png",
            "right_imgs": "/storage/pmj/zhi/dataset/Booster/test/balanced/*/camera_02/im0.png",
            "output_dir": "output/booster",
            "checkpoint": "checkpoints/pretrain/step_25000_igev-stereo-ddp.pth"
        }
    }
    return configs.get(dataset, configs["booster"])  # 默认返回booster配置

def run_one_dataset(args, dataset_name):
    # 基于数据集名派生配置（可被命令行覆盖）
    cfg = get_dataset_config(dataset_name)
    left_imgs = args.left_imgs if args.left_imgs is not None else cfg["left_imgs"]
    right_imgs = args.right_imgs if args.right_imgs is not None else cfg["right_imgs"]
    output_dir = args.output_directory if args.output_directory is not None else os.path.join(cfg["output_dir"]) 
    restore_ckpt = args.restore_ckpt if args.restore_ckpt is not None else cfg["checkpoint"]

    model = torch.nn.DataParallel(Monster(args), device_ids=[0])
    checkpoint = torch.load(restore_ckpt, weights_only=False)

    # 智能处理不同格式的检查点
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    elif 'state_dict' in checkpoint:
        state_dict = checkpoint['state_dict']
    else:
        state_dict = checkpoint

    # 准备加载的状态字典
    model_state = model.state_dict()
    matched_params = {}
    for name, param in state_dict.items():
        model_name = name
        if not name.startswith('module.') and 'module.' + name in model_state:
            model_name = 'module.' + name
        elif name.startswith('module.') and name[7:] in model_state:
            model_name = name[7:]
        if model_name in model_state and model_state[model_name].shape == param.shape:
            matched_params[model_name] = param
    try:
        model.load_state_dict(matched_params, strict=True)
    except Exception:
        ckpt = {}
        for key in state_dict:
            if key.startswith('module.'):
                ckpt[key] = state_dict[key]
            else:
                ckpt['module.' + key] = state_dict[key]
        model.load_state_dict(ckpt, strict=False)

    model = model.module
    model.to(DEVICE)
    model.eval()

    output_directory = Path(output_dir)
    output_directory.mkdir(parents=True, exist_ok=True)

    with torch.no_grad():
        left_images = sorted(glob.glob(left_imgs, recursive=True))
        right_images = sorted(glob.glob(right_imgs, recursive=True))
        print(f"[{dataset_name}] Found {len(left_images)} images. Saving files to {output_directory}/")

        for (imfile1, imfile2) in tqdm(list(zip(left_images, right_images))):
            image1 = load_image(imfile1)
            image2 = load_image(imfile2)

            if dataset_name == "booster":
                image1 = image1[:,:,::2,::2]
                image2 = image2[:,:,::2,::2]

            padder = InputPadder(image1.shape, divis_by=32)
            image1, image2 = padder.pad(image1, image2)

            disp, init_disp, conf = model(image1, image2, iters=args.valid_iters, test_mode=True)

            disp = disp.cpu().numpy()
            disp = padder.unpad(disp)
            init_disp = padder.unpad(init_disp.cpu().numpy())
            conf_np = padder.unpad(conf.clamp(0.0, 1.0).cpu().numpy())
            # p_occ_np = padder.unpad(p_occ.clamp(0.0, 1.0).cpu().numpy())

            file_stem = get_file_stem(imfile1, dataset_name)
            left_img_np = np.array(Image.open(imfile1))
            if dataset_name == "booster":
                left_img_np = left_img_np[::2, ::2]

            if args.save_separate:
                disp_final = disp.squeeze()
                print("disp_final: ", disp_final.min().item(), disp_final.max().item())
                plt.imsave(os.path.join(output_directory, f"{file_stem}_disp.png"), disp_final, cmap="jet")
                init_disp_final = init_disp.squeeze()
                print("init_disp_final: ", init_disp_final.min().item(), init_disp_final.max().item())
                plt.imsave(os.path.join(output_directory, f"{file_stem}_init_disp.png"), init_disp_final, cmap="jet")
                conf_final = conf_np.squeeze()
                plt.imsave(os.path.join(output_directory, f"{file_stem}_conf.png"), conf_final, cmap="gray", vmin=0.0, vmax=1.0)
                # pocc_final = p_occ_np.squeeze()
                # plt.imsave(os.path.join(output_directory, f"{file_stem}_pocc.png"), pocc_final, cmap="gray", vmin=0.0, vmax=1.0)
            print(f"[{dataset_name}] {file_stem} saved to {output_directory}")


def demo(args):
    datasets = args.datasets
    for ds in datasets:
        run_one_dataset(args, ds)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    
    # 数据集选择：支持多选
    parser.add_argument("--datasets", nargs='+', type=str, choices=["sceneflow", "md_F", "md_H", "kitti", "drivingstereo", "booster"], 
                        default=["sceneflow", "md_H", "md_F"], help="选择一个或多个数据集")
    
    # 这些参数可以被数据集配置覆盖，但也可以手动指定
    parser.add_argument("--restore_ckpt", help="restore checkpoint", default="checkpoints/sceneflow/12000.pth")
    parser.add_argument("-l", "--left_imgs", default=None, help="path to all first (left) frames")
    parser.add_argument("-r", "--right_imgs", default=None, help="path to all second (right) frames")
    parser.add_argument("--output_directory", default=None, help="directory to save output")
    
    parser.add_argument("--save_numpy", action="store_true", help="save output as numpy arrays")
    parser.add_argument("--save_separate", default=True, help="also save separate disparity and monodepth images")
    parser.add_argument("--mixed_precision", action="store_true", help="use mixed precision")
    parser.add_argument("--precision_dtype",default="float32",choices=["float16", "bfloat16", "float32"],help="Choose precision type: float16 or bfloat16 or float32")
    parser.add_argument("--valid_iters",type=int,default=32,help="number of flow-field updates during forward pass")

    # Architecture choices
    parser.add_argument('--encoder', type=str, default='vitl', choices=['vits', 'vitb', 'vitl', 'vitg'])
    parser.add_argument('--hidden_dims', nargs='+', type=int, default=[128]*3, help="hidden state and context dimensions")
    parser.add_argument('--corr_implementation', choices=["reg", "alt", "reg_cuda", "alt_cuda"], default="reg", help="correlation volume implementation")
    parser.add_argument('--shared_backbone', action='store_true', help="use a single backbone for the context and feature encoders")
    parser.add_argument('--corr_levels', type=int, default=2, help="number of levels in the correlation pyramid")
    parser.add_argument('--corr_radius', type=int, default=4, help="width of the correlation pyramid")
    parser.add_argument('--n_downsample', type=int, default=2, help="resolution of the disparity field (1/2^K)")
    parser.add_argument('--slow_fast_gru', action='store_true', help="iterate the low-res GRUs more frequently")
    parser.add_argument('--n_gru_layers', type=int, default=3, help="number of hidden GRU levels")
    parser.add_argument('--max_disp', type=int, default=192, help="max disp of geometry encoding volume")

    args = parser.parse_args()
    
    # 多数据集依次运行，各自使用默认配置（若未被命令行覆盖）
    print(f"使用数据集: {args.datasets}")
    print("-" * 60)
    demo(args)
    
