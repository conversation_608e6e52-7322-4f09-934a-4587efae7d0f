wandb: {}
project_name: sceneflow
restore_ckpt: 'checkpoints/sceneflow/26000.pth'
logdir: './checkpoints/sceneflow/'
encoder: 'vitl'
batch_size: 12
train_datasets: ['sceneflow']
lr: 5e-5
wdecay: 1e-5
total_step: 200000
save_frequency: 2000
save_path: ./checkpoints/sceneflow/
val_frequency: 5000
image_size: [416, 832]
train_iters: 22
valid_iters: 32
val_dataset: 'kitti'
corr_implementation: "reg"
corr_levels: 2
corr_radius: 4
n_downsample: 2
n_gru_layers: 3
hidden_dims: [128, 128, 128]
max_disp: 192
saturation_range: [0.0, 1.4]
do_flip: False
spatial_scale: [-0.2, 0.4]
noyjitter: False
num_gpu: 4
seed: 2025
